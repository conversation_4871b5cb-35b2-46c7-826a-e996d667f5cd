# Hallite Configurator API Docs

## Install Required Packages

Install Scoop for installing dependencies:

```
Set-ExecutionPolicy RemoteSigned -Scope CurrentUser; iwr -useb get.scoop.sh | iex
scoop install git sqlite
```

Install Doppler:

```
scoop bucket add doppler https://github.com/DopplerHQ/scoop-doppler.git
scoop install doppler
```

Install uv:

```
powershell -ExecutionPolicy ByPass -c "irm https://astral.sh/uv/install.ps1 | iex"
```

Allow user to execute PowerShell scripts to activate virtual envs:

```
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

Install global Python packages and add pipx local bin to path:

```
pip install pipx ipython
$env:Path = "C:\Users\<USER>\AppData\Roaming\Python\Python313\Scripts;$env:Path"
```

Create a PowerShell profile and add PATH statements for Python package binaries:

```
# Create PowerShell profile if it doesn't exist
New-Item -Path $PROFILE -Type File -Force

# Add PATH statements to profile
Add-Content -Path $PROFILE -Value '$env:Path = "C:\Users\<USER>\.local\bin;$env:Path"'
Add-Content -Path $PROFILE -Value '$env:Path = "C:\Users\<USER>\AppData\Roaming\Python\Python313\Scripts;$env:Path"'
Add-Content -Path $PROFILE -Value '$env:Path = "C:\Users\<USER>\scoop\shims;$env:Path"'
```

## Setup

Create the application directory:

```
mkdir C:\Apps
git clone https://github.com/ryan-blunden/hallite-configurator-api.git C:\Apps
```

Create Doppler service token and register:

```
doppler configure set token "TOKEN" --scope "C:\Apps\hallite-configurator-api"
```

Install dependencies:

```
uv venv --clear
just app-install-dependencies
```

Seed the databaase:

```
doppler run --
doppler run -- just app-load-base-data
```

Configure firewall ports by opening a new PowerShell shell as an Administrator:

```powershell
# Run PowerShell as Administrator, then execute:
$ports = @(80, 443, 8080, 9090)

foreach ($port in $ports) {
    New-NetFirewallRule `
        -DisplayName "Allow inbound TCP port $port" `
        -Direction Inbound `
        -LocalPort $port `
        -Protocol TCP `
        -Action Allow `
        -Profile Any
}
```

Currently, local firewall changes are overridden by policies at the network domain level so the firewall can be disabled by running:

```
Set-NetFirewallProfile -Profile Domain,Public,Private -Enabled False
```

Then enable it once our local rules can be applied:

```
Set-NetFirewallProfile -Profile Domain,Public,Private -Enabled True
```

## Deployment

Because it's an API-only application, it's run as a Windows service using PyWin32 and uses Waitress to run Django which is easier and more lightweight than the typical Apcahe or IIS + mod_wsgi.

