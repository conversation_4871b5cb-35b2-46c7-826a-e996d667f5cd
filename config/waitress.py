# -*- coding: utf-8 -*-
"""
Waitress WSGI server configuration for Windows development/production.

This configuration mirrors the gunicorn.py settings to provide a
Windows-compatible alternative with the same environment variables.

ARCHITECTURE DIFFERENCES:
- Gunicorn: Multi-process (workers) + multi-threaded (threads per worker)
- Waitress: Single-process + multi-threaded only
- Waitress cannot spawn multiple processes like gun<PERSON>

THREAD RECOMMENDATIONS:
- CPU-bound tasks: threads = CPU cores (typically 4-8)
- I/O-bound tasks: threads = 2-4x CPU cores (8-32)
- Mixed workload: Start with 2x CPU cores and tune based on performance
"""

import multiprocessing
import os

from waitress import serve

from config.wsgi import application

# Mirror gunicorn configuration
bind_host = "0.0.0.0"  # Convert from "[::]:9090" to waitress format
bind_port = 9090
workers = int(os.getenv("WEB_CONCURRENCY", f"{multiprocessing.cpu_count() * 2}"))
threads = int(os.getenv("PYTHON_MAX_THREADS", "1"))
timeout = int(os.getenv("WEB_TIMEOUT", "120"))

# Waitress threading strategy:
# Since waitress is single-process, we use the total concurrency from gun<PERSON>
# (workers * threads) as our thread count for similar performance
cpu_count = multiprocessing.cpu_count()
if threads == 1:
    # If PYTHON_MAX_THREADS is default (1), use a more reasonable thread count
    # for I/O-bound Django applications (Excel operations, DB queries)
    total_threads = min(workers, cpu_count * 4)  # Cap at 4x CPU cores
else:
    # Use gunicorn's calculation if PYTHON_MAX_THREADS is explicitly set
    total_threads = workers * threads

# Ensure minimum and maximum thread bounds
total_threads = max(4, min(total_threads, 64))  # Between 4-64 threads

if __name__ == "__main__":
    print(f"Starting Waitress server on {bind_host}:{bind_port}")
    print(f"CPU cores: {cpu_count}")
    print(f"Configured threads: {total_threads}")
    print(f"  - Based on WEB_CONCURRENCY={workers}, PYTHON_MAX_THREADS={threads}")
    print("  - Capped between 4-64 threads for optimal performance")
    print(f"Timeout: {timeout} seconds")
    print("")
    print("Note: Waitress is single-process (unlike gunicorn's multi-process)")
    print("Use multiple Waitress instances behind a load balancer for true multi-process")

    serve(
        application,
        host=bind_host,
        port=bind_port,
        threads=total_threads,
        channel_timeout=timeout,
        url_scheme="http",
        # Enable request logging similar to gunicorn's accesslog
        ident="Hallite-Configurator-API",
    )
