# type: ignore

import uuid
from unittest.mock import Mock, patch

from django.test import TestCase
from ninja.testing import TestClient

from configurator.router import router
from configurator.schemas import ProfileOutputs


class ProfileEndpointTest(TestCase):

    def setUp(self):
        self.client = TestClient(router)
        self.session_id = str(uuid.uuid4())

    def test_missing_session_id_returns_400(self):
        response = self.client.get("/profile/HK110")
        self.assertEqual(response.status_code, 400)
        self.assertIn("Missing x-configurator-session-id header", response.json()["detail"])

    def test_invalid_session_id_returns_400(self):
        response = self.client.get("/profile/HK110", headers={"x-configurator-session-id": "invalid-uuid"})
        self.assertEqual(response.status_code, 400)
        self.assertIn("Invalid x-configurator-session-id format", response.json()["detail"])

    def test_invalid_profile_code_returns_422(self):
        response = self.client.get("/profile/INVALID", headers={"x-configurator-session-id": self.session_id})
        self.assertEqual(response.status_code, 422)
        error_data = response.json()
        self.assertIn("detail", error_data)
        self.assertIn("Invalid profile", str(error_data["detail"]))

    @patch("configurator.router.WorkbookSessionService")
    def test_successful_profile_data_returns_200(self, mock_session_service_class):
        # Create a proper ProfileOutputs object
        mock_profile_outputs = ProfileOutputs.model_validate(
            {
                "profile": "HK110",
                "profile_size": "SVP",
                "measure": {
                    "measure": "Metric",
                    "od": 200.0,
                    "id": 180.0,
                    "ch": 50.0,
                },
                "extra": {"key": "Qty Vees", "value": 4},
                "material1": {"name": "Vee Material", "value": "NB85"},
                "material2": {"name": "T/A Material", "value": "POMD"},
                "material3": {"name": "B/A Material", "value": "POMD"},
            }
        )

        # Setup mocks
        mock_session_service = Mock()
        mock_workbook_service = Mock()
        mock_workbook_service.get_profile_data.return_value = mock_profile_outputs
        # Add context manager support
        mock_workbook_service.__enter__ = Mock(return_value=mock_workbook_service)
        mock_workbook_service.__exit__ = Mock(return_value=None)
        mock_session_service.open_workbook_session.return_value = mock_workbook_service
        mock_session_service_class.get_service.return_value = mock_session_service

        # Make request
        response = self.client.get("/profile/HK110", headers={"x-configurator-session-id": self.session_id})

        # Verify response
        self.assertEqual(response.status_code, 200)
        data = response.json()

        self.assertEqual(data["profile"], "HK110")
        self.assertEqual(data["profile_size"], "SVP")
        self.assertEqual(data["extra"], "Qty Vees")
        self.assertEqual(data["material1"], "NB85")
        self.assertEqual(data["material2"], "POMD")
        self.assertEqual(data["material3"], "POMD")

        # Verify the service was called correctly
        mock_session_service.open_workbook_session.assert_called_once_with(self.session_id)
        mock_workbook_service.get_profile_data.assert_called_once_with("HK110")

    @patch("configurator.router.WorkbookSessionService")
    def test_workbook_service_error_returns_422(self, mock_session_service_class):
        # Setup mocks to raise an error
        mock_session_service = Mock()
        mock_workbook_service = Mock()
        mock_workbook_service.get_profile_data.side_effect = Exception("Test workbook error")
        # Add context manager support
        mock_workbook_service.__enter__ = Mock(return_value=mock_workbook_service)
        mock_workbook_service.__exit__ = Mock(return_value=None)
        mock_session_service.open_workbook_session.return_value = mock_workbook_service
        mock_session_service_class.get_service.return_value = mock_session_service

        # Make request
        response = self.client.get("/profile/HK110", headers={"x-configurator-session-id": self.session_id})

        # Verify error response
        self.assertEqual(response.status_code, 422)
        error_data = response.json()
        self.assertIn("detail", error_data)

        # Verify session cleanup was called
        mock_session_service.cleanup_session.assert_called_once_with(self.session_id)
