# type: ignore

"""
Simple unit tests for ExcelService using real Excel file.
"""

import os
import platform
from pathlib import Path
from unittest import skipIf
from unittest.mock import Mock

from django.test import SimpleTestCase

from configurator.schemas import WorkbookInputs
from configurator.services import (
    ExcelService,
    ExcelServiceError,
    WorkbookNotFoundError,
    WorkbookService,
)

# Skip Excel tests in CI or on Linux (where Excel is not available)
SKIP_EXCEL_TESTS = os.getenv("CI") == "true" or platform.system() == "Linux" or os.getenv("GITHUB_ACTIONS") == "true"


class ExcelServiceTest(SimpleTestCase):

    def test_init_validates_path(self):
        with self.assertRaises(WorkbookNotFoundError):
            ExcelService(Path("/nonexistent/file.xlsm"))

    def test_init_requires_path(self):
        with self.assertRaises(ExcelServiceError):
            ExcelService("")

    @skipIf(SKIP_EXCEL_TESTS, "Excel not available in CI/Linux environment")
    def test_open_and_close(self):
        service = ExcelService.open(Path("workbook/E-quote v7.1.xlsm"))
        self.assertIsNotNone(service.workbook)
        service.close()
        self.assertIsNone(service.workbook)

    @skipIf(SKIP_EXCEL_TESTS, "Excel not available in CI/Linux environment")
    def test_write_and_read_cell(self):
        service = ExcelService.open(Path("workbook/E-quote v7.1.xlsm"))

        original_value = service.read_cell("profileentry")
        service.write_cell("profileentry", "HK109")
        read_value = service.read_cell("extratype")

        self.assertEqual(read_value, "Wear ring leg length")

        service.write_cell("profileentry", original_value)
        service.close()

    @skipIf(SKIP_EXCEL_TESTS, "Excel not available in CI/Linux environment")
    def test_execute_macro(self):
        service = ExcelService.open(Path("workbook/E-quote v7.1.xlsm"))
        service.execute_macro("runme")
        service.close()

    @skipIf(SKIP_EXCEL_TESTS, "Excel not available in CI/Linux environment")
    def test_methods_require_open_workbook(self):
        service = ExcelService(Path("workbook/E-quote v7.1.xlsm"))

        with self.assertRaises(ExcelServiceError):
            service.write_cell("ODentry", "70.000")

        with self.assertRaises(ExcelServiceError):
            service.read_cell("ODentry")

        with self.assertRaises(ExcelServiceError):
            service.execute_macro("ODentry")


class WorkbookServiceTest(SimpleTestCase):
    """Tests for ExcelService input writing functionality."""

    def setUp(self):
        """Set up test fixtures."""
        self.mock_excel_service = Mock(spec=ExcelService)
        # Add the missing workbook_path attribute
        self.mock_excel_service.workbook_path = Path("workbook/E-quote v7.1.xlsm")
        self.configurator_service = WorkbookService(self.mock_excel_service)

        # Use hardcoded HK110 test data
        self.test_inputs = WorkbookInputs(
            profile="HK110",
            measure="Metric",
            od=float("200.000"),
            id=float("180.000"),
            ch=float("50.000"),
            extra=4,  # Qty Vees from HK110
            material1="TMB",
            material2="NB85",
            material3=None,
        )

    def test_write_inputs_calls_excel_service_in_correct_sequence(self):
        """Test that write_inputs calls ExcelService in the correct sequence."""
        # Mock the validation cells to return appropriate values
        self.mock_excel_service.read_cell.side_effect = lambda cell: {
            "extratype": "Wear ring leg length",
            "type2": "T/A Material",  # This requires material2
            "Type3": "",  # Empty, so material3 not required
        }.get(cell, "")

        # Call write_inputs
        self.configurator_service._write_inputs(self.test_inputs)

        # Verify the sequence of calls (using HK110 fixture data)
        expected_calls = [
            ("profileentry", "HK110"),
            ("measureentry", "Metric"),
            ("ODentry", float("200.000")),
            ("IDentry", float("180.000")),
            ("CHentry", float("50.000")),
            ("extraentry", 4),
            ("MATL1entry", "TMB"),
            ("MATL2entry", "NB85"),
        ]

        # Check that write_cell was called with the correct arguments
        actual_calls = self.mock_excel_service.write_cell.call_args_list
        self.assertEqual(len(actual_calls), len(expected_calls))

        for i, (expected_cell, expected_value) in enumerate(expected_calls):
            actual_call = actual_calls[i]
            self.assertEqual(actual_call[0][0], expected_cell)  # cell name
            self.assertEqual(actual_call[0][1], expected_value)  # value

    def test_write_inputs_skips_none_materials(self):
        """Test that None materials are skipped in the input sequence."""
        # Create inputs with only material1 and no extra
        inputs_with_one_material = WorkbookInputs(
            profile="HK110",
            measure="Metric",
            od=float("200.000"),
            id=float("180.000"),
            ch=float("50.000"),
            extra=None,  # No extra value
            material1="TMB",
            material2=None,  # No material2
            material3=None,  # No material3
        )

        # Mock validation cells to not require extra materials
        self.mock_excel_service.read_cell.side_effect = lambda cell: {
            "extratype": "",  # No extra required
            "type2": "",  # No material2 required
            "Type3": "",  # No material3 required
        }.get(cell, "")

        # Call write_inputs
        self.configurator_service._write_inputs(inputs_with_one_material)

        # Verify that only material1 was written, not material2 or material3
        actual_calls = self.mock_excel_service.write_cell.call_args_list
        cell_names = [call[0][0] for call in actual_calls]

        self.assertIn("MATL1entry", cell_names)
        self.assertNotIn("MATL2entry", cell_names)
        self.assertNotIn("MATL3entry", cell_names)
        self.assertNotIn("extraentry", cell_names)  # extra was None

    def test_write_inputs_handles_extratype_read_error(self):
        """Test that write_inputs handles errors when reading extratype gracefully."""
        # Mock validation cells to raise an exception (this will cause validation to fail)
        self.mock_excel_service.read_cell.side_effect = Exception("Cell not found")
        self.mock_excel_service.read_cells_batch.side_effect = Exception("Cell not found")

        # Should raise an exception due to validation failure
        with self.assertRaises(Exception):
            self.configurator_service._write_inputs(self.test_inputs)

    def test_read_outputs_returns_structured_data(self):
        """Test that read_outputs returns properly structured WorkbookOutputs."""
        # Mock all the Excel cell reads with realistic data
        mock_cell_values = {
            # Profile outputs
            "profileentry": "HK110",
            "D6": "HK110-REF",
            "measureentry": "Metric",
            "ODentry": 200.000,
            "IDentry": 180.000,
            "CHentry": 50.000,
            "extratype": "Qty Vees",
            "extraentry": 4,
            "MATL1entry": "NB85",
            "MATL2entry": "POMD",
            "MATL3entry": "POMD",
            # Material type cells
            "Type1": "Vee Material",
            "type2": "T/A Material",
            "Type3": "B/A Material",
            # Part info
            "C31": "HK110-200-180-50",
            "partdesc": "HK110: 180.000 x 200.000 x 50.000 NB85",
            # Pricing (from HK110.csv)
            "costpriceevco": 216.81,
            "C19": 1734.48,
            "C20": 2168.10,
            "C21": 2710.13,
            # Materials (from HK110.csv)
            "billetreqd1": "BTNB85-170/205",
            "mctime1": 12.8,
            "mmreqd1": "34.8mm   $40.57",
            "billetreqd2": "BTPOMD-170/230",
            "mctime2": 11.9,
            "mmreqd2": "20.1mm   $29.44",
            "billetreqd3": "BTPOMD-170/230",
            "mctime3": 14.9,
            "mmreqd3": "27.1mm   $39.55",
        }

        def mock_read_cell(cell_name):
            return mock_cell_values.get(cell_name, None)

        self.mock_excel_service.read_cell.side_effect = mock_read_cell
        # Mock the batch read function used by _read_outputs
        self.mock_excel_service.read_cells_batch.return_value = mock_cell_values

        # Call read_outputs
        result = self.configurator_service._read_outputs()  # type: WorkbookOutputs

        # Verify the structure and key values (using HK110 data)
        # pylint: disable=no-member  # False positive - result is WorkbookOutputs
        self.assertEqual(result.profile_outputs.profile, "HK110")
        self.assertEqual(result.profile_outputs.profile_size, "HK110-REF")
        self.assertEqual(result.profile_outputs.measure.measure, "Metric")
        self.assertEqual(result.profile_outputs.measure.od, float("200.000"))
        self.assertEqual(result.profile_outputs.extra.key, "Qty Vees")
        self.assertEqual(result.profile_outputs.extra.value, 4)

        self.assertEqual(result.part.code, "HK110-200-180-50")
        self.assertEqual(result.part.description, "HK110: 180.000 x 200.000 x 50.000 NB85")

        self.assertEqual(result.price.cost, "216.81")
        self.assertEqual(result.price.retail, "2710.13")

        self.assertEqual(result.materials.material1.billet_required, "BTNB85-170/205")
        self.assertEqual(result.materials.material1.min_manufacturing_time, float("12.8"))
        self.assertIsNotNone(result.materials.material2)
        self.assertIsNotNone(result.materials.material3)  # HK110 has 3 materials

    def test_process_success_flow(self):
        """Test the complete success flow: write → refresh check → error check → read."""
        # Mock successful processing with minimal required output cells
        mock_cell_values = {
            "costpriceevco": 216.81,
            "partdesc": "HK110: 180.000 x 200.000 x 50.000 NB85",
            "C31": "HK110-200-180-50",
            "profileentry": "HK110",
            "D6": "HK110-REF",
            "measureentry": "Metric",
            "ODentry": 200.000,
            "IDentry": 180.000,
            "CHentry": 50.000,
            "extratype": "Qty Vees",  # Matches test_inputs.extra=4
            "extraentry": 4,
            "MATL1entry": "NB85",
            "D7": "Metric",
            "D8": 200.000,
            "D9": 180.000,
            "D10": 50.000,
            "Type1": "Vee Material",
            "type2": "T/A Material",  # Matches test_inputs.material2="NB85"
            "Type3": "",  # Empty, so material3=None is OK
            "C19": 270.51,
            "C20": 1355.06,
            "C21": 2710.13,
            "billetreqd1": "BTNB85-170/205",
            "mctime1": 12.8,
            "mmreqd1": 0.0,
        }
        self.mock_excel_service.read_cell.side_effect = lambda cell: mock_cell_values.get(cell, None)
        self.mock_excel_service.read_cells_batch.return_value = mock_cell_values

        result, error = self.configurator_service.process(self.test_inputs)

        # Verify inputs were written and outputs were read correctly
        self.assertTrue(self.mock_excel_service.write_cell.called)
        self.assertEqual(result.profile_outputs.profile, "HK110")
        self.assertEqual(result.price.cost, "216.81")

    def test_needs_refresh_detects_refresh_data(self):
        """Test that needs_refresh detects 'refresh data' in part description."""
        self.mock_excel_service.read_cell.return_value = " refresh data "

        result = self.configurator_service._needs_refresh()

        self.assertTrue(result)

    def test_detect_error_with_zero_cost_price(self):
        """Test that detect_error detects zero cost price and returns sheet error."""
        mock_cell_values = {
            "costpriceevco": "0.00",
            "partdesc": "ID too small. Check with Technical.",
        }
        # Mock read_cell since _detect_error and _detect_material_unavailable use individual cell reads
        self.mock_excel_service.read_cell.side_effect = lambda cell: mock_cell_values.get(cell, None)

        result = self.configurator_service._detect_error()

        self.assertEqual(result, "ID too small. Check with Technical.")

    def test_process_with_error_raises_exception(self):
        """Test that process raises exception when errors are detected."""
        mock_cell_values = {
            "costpriceevco": "0.00",  # This will trigger an error
            "partdesc": "ID too small. Check with Technical.",
            # Add validation cells to pass validation
            "extratype": "Qty Vees",  # Matches test_inputs.extra=4
            "type2": "T/A Material",  # Matches test_inputs.material2="NB85"
            "Type3": "",  # Empty, so material3=None is OK
        }
        self.mock_excel_service.read_cell.side_effect = lambda cell: mock_cell_values.get(cell, None)
        self.mock_excel_service.read_cells_batch.return_value = mock_cell_values

        result, error = self.configurator_service.process(self.test_inputs)
        self.assertIsNone(result)
        self.assertIsNotNone(error)

    def test_validate_requires_extra_when_extratype_present(self):
        """Test that validate returns False when extra value is missing and extratype is not empty."""
        # Setup mock to return extratype value
        mock_cell_values = {
            "extratype": "Qty Vees",
            "type2": "",
            "Type3": "",
        }
        self.mock_excel_service.read_cell.side_effect = lambda cell: mock_cell_values.get(cell, "")
        self.mock_excel_service.read_cells_batch.return_value = mock_cell_values

        inputs = WorkbookInputs(
            profile="HS110",
            measure="metric",
            od=float("100.0"),
            id=float("80.0"),
            ch=float("10.0"),
            material1="EPDM",
            extra=None,  # Missing extra value
        )

        result = self.configurator_service._validate(inputs)
        self.assertFalse(result)

    def test_validate_requires_material2_when_type2_present(self):
        """Test that validate returns False when material2 is missing and type2 is not empty."""
        # Setup mock to return type2 value
        mock_cell_values = {
            "extratype": "",
            "type2": "NB85",
            "Type3": "",
        }
        self.mock_excel_service.read_cell.side_effect = lambda cell: mock_cell_values.get(cell, "")
        self.mock_excel_service.read_cells_batch.return_value = mock_cell_values

        inputs = WorkbookInputs(
            profile="HS110",
            measure="metric",
            od=float("100.0"),
            id=float("80.0"),
            ch=float("10.0"),
            material1="EPDM",
            material2=None,  # Missing material2 value
        )

        result = self.configurator_service._validate(inputs)
        self.assertFalse(result)

    def test_validate_requires_material3_when_type3_present(self):
        """Test that validate returns False when material3 is missing and type3 is not empty."""
        # Setup mock to return type3 value
        mock_cell_values = {
            "extratype": "",
            "type2": "",
            "Type3": "POMD",
        }
        self.mock_excel_service.read_cell.side_effect = lambda cell: mock_cell_values.get(cell, "")
        self.mock_excel_service.read_cells_batch.return_value = mock_cell_values

        inputs = WorkbookInputs(
            profile="HS110",
            measure="metric",
            od=float("100.0"),
            id=float("80.0"),
            ch=float("10.0"),
            material1="EPDM",
            material3=None,  # Missing material3 value
        )

        result = self.configurator_service._validate(inputs)
        self.assertFalse(result)

    def test_validate_passes_when_all_required_fields_provided(self):
        """Test that validate returns True when all required fields are provided."""
        # Setup mock to return values requiring extra and materials
        mock_cell_values = {
            "extratype": "Qty Vees",
            "type2": "NB85",
            "Type3": "POMD",
        }
        self.mock_excel_service.read_cell.side_effect = lambda cell: mock_cell_values.get(cell, "")
        self.mock_excel_service.read_cells_batch.return_value = mock_cell_values

        inputs = WorkbookInputs(
            profile="HS110",
            measure="metric",
            od=float("100.0"),
            id=float("80.0"),
            ch=float("10.0"),
            material1="EPDM",
            extra=25,  # Provided
            material2="TEFV",  # Provided
            material3="PEEK",  # Provided
        )

        result = self.configurator_service._validate(inputs)
        self.assertTrue(result)

    def test_validate_passes_when_no_requirements(self):
        """Test that validate returns True when workbook has no special requirements."""
        # Setup mock to return empty values
        mock_cell_values = {
            "extratype": "",
            "type2": "",
            "Type3": "",
        }
        self.mock_excel_service.read_cell.side_effect = lambda cell: mock_cell_values.get(cell, "")
        self.mock_excel_service.read_cells_batch.return_value = mock_cell_values

        inputs = WorkbookInputs(
            profile="HS110",
            measure="metric",
            od=float("100.0"),
            id=float("80.0"),
            ch=float("10.0"),
            material1="EPDM",
            # No extra, material2, or material3 needed
        )

        result = self.configurator_service._validate(inputs)
        self.assertTrue(result)

    def test_validate_rejects_extra_when_extratype_empty(self):
        """Test that validate returns False when extra value is provided but extratype is empty."""
        # Setup mock to return empty extratype
        mock_cell_values = {
            "extratype": "",
            "type2": "",
            "Type3": "",
        }
        self.mock_excel_service.read_cell.side_effect = lambda cell: mock_cell_values.get(cell, "")
        self.mock_excel_service.read_cells_batch.return_value = mock_cell_values

        inputs = WorkbookInputs(
            profile="HS110",
            measure="metric",
            od=float("100.0"),
            id=float("80.0"),
            ch=float("10.0"),
            material1="EPDM",
            extra=25,  # Should not be provided when extratype is empty
        )

        result = self.configurator_service._validate(inputs)
        self.assertFalse(result)

    def test_validate_rejects_material2_when_type2_empty(self):
        """Test that validate returns False when material2 is provided but type2 is empty."""
        # Setup mock to return empty type2
        mock_cell_values = {
            "extratype": "",
            "type2": "",
            "Type3": "",
        }
        self.mock_excel_service.read_cell.side_effect = lambda cell: mock_cell_values.get(cell, "")
        self.mock_excel_service.read_cells_batch.return_value = mock_cell_values

        inputs = WorkbookInputs(
            profile="HS110",
            measure="metric",
            od=float("100.0"),
            id=float("80.0"),
            ch=float("10.0"),
            material1="EPDM",
            material2="TEFV",  # Should not be provided when type2 is empty
        )

        result = self.configurator_service._validate(inputs)
        self.assertFalse(result)

    def test_validate_rejects_material3_when_type3_empty(self):
        """Test that validate returns False when material3 is provided but type3 is empty."""
        # Setup mock to return empty type3
        self.mock_excel_service.read_cell.side_effect = lambda cell: {
            "extratype": "",
            "type2": "",
            "Type3": "",
        }.get(cell, "")

        inputs = WorkbookInputs(
            profile="HS110",
            measure="metric",
            od=float("100.0"),
            id=float("80.0"),
            ch=float("10.0"),
            material1="EPDM",
            material3="PEEK",  # Should not be provided when type3 is empty
        )

        result = self.configurator_service._validate(inputs)
        self.assertFalse(result)

    def test_write_inputs_validates_after_profile_written(self):
        """Test that validation happens after profile is written, not before."""
        # Track whether profile has been written
        profile_written = False

        def mock_write_cell(cell, value):
            nonlocal profile_written
            if cell == "profileentry":
                profile_written = True

        def mock_read_cell(cell):
            # Return values that require extra/materials only after profile is written
            if profile_written:
                return {
                    "extratype": "Qty Vees",  # Now requires extra
                    "type2": "NB85",  # Now requires material2
                    "Type3": "",  # Still empty
                }.get(cell, "")
            else:
                return {
                    "extratype": "",
                    "type2": "",
                    "Type3": "",
                }.get(cell, "")

        def mock_read_cells_batch(cells):
            # Return values that require extra/materials only after profile is written
            if profile_written:
                return {
                    "extratype": "Qty Vees",  # Now requires extra
                    "type2": "NB85",  # Now requires material2
                    "Type3": "",  # Still empty
                }
            else:
                return {
                    "extratype": "",
                    "type2": "",
                    "Type3": "",
                }

        self.mock_excel_service.write_cell.side_effect = mock_write_cell
        self.mock_excel_service.read_cell.side_effect = mock_read_cell
        self.mock_excel_service.read_cells_batch.side_effect = mock_read_cells_batch

        # Create inputs that would fail validation if checked before profile is written
        # but should pass validation after profile is written
        inputs = WorkbookInputs(
            profile="HS110",
            measure="metric",
            od=float("100.0"),
            id=float("80.0"),
            ch=float("10.0"),
            material1="EPDM",
            extra=25,  # Provided - should be valid after profile sets extratype
            material2="TEFV",  # Provided - should be valid after profile sets type2
        )

        # This should succeed because validation happens after profile is written
        result = self.configurator_service._write_inputs(inputs)
        self.assertTrue(result)  # Should return True since all required fields are provided

        # Verify that profile was written first
        write_calls = self.mock_excel_service.write_cell.call_args_list
        first_call = write_calls[0]
        self.assertEqual(first_call[0][0], "profileentry")  # First call should be profile
        self.assertEqual(first_call[0][1], "HS110")

    def test_process_skips_pricing_when_validation_fails(self):
        """Test that process skips pricing calculation when validation fails."""
        # Setup mock to return values that require extra, but don't provide it
        mock_cell_values = {
            "extratype": "Qty Vees",  # Requires extra
            "type2": "",
            "Type3": "",
            "costpriceevco": 100.0,  # Non-zero cost price (no error)
            "partdesc": "Valid part",
            # Add required values for _read_outputs
            "profileentry": "HS110",
            "measureentry": "metric",
            "ODentry": 100.0,
            "IDentry": 80.0,
            "CHentry": 10.0,
            "extraentry": None,  # No extra value provided (validation should fail)
            "MATL1entry": "EPDM",
            "D6": "REF123",
            "D7": "metric",
            "D8": 100.0,
            "D9": 80.0,
            "D10": 10.0,
            "Type1": "Standard",
            "C31": "PART123",
            "C19": 150.0,
            "C20": 200.0,
            "C21": 250.0,
            "billetreqd1": "Yes",
            "mctime1": 30.0,
            "mmreqd1": "Material1",
        }
        self.mock_excel_service.read_cell.side_effect = lambda cell: mock_cell_values.get(cell, "")
        self.mock_excel_service.read_cells_batch.return_value = mock_cell_values

        inputs = WorkbookInputs(
            profile="HS110",
            measure="metric",
            od=float("100.0"),
            id=float("80.0"),
            ch=float("10.0"),
            material1="EPDM",
            # Missing extra value - validation should fail
        )

        # Process should complete but skip pricing calculation
        outputs, error = self.configurator_service.process(inputs)

        # Should return outputs (not None) and no error
        self.assertIsNotNone(outputs)
        self.assertIsNone(error)

        # Verify that execute_macro (pricing calculation) was NOT called
        self.mock_excel_service.execute_macro.assert_not_called()

    def test_process_runs_pricing_when_validation_passes(self):
        """Test that process runs pricing calculation when validation passes."""
        # Setup mock to return values that require extra, and provide it
        mock_cell_values = {
            "extratype": "Qty Vees",  # Requires extra
            "type2": "",
            "Type3": "",
            "costpriceevco": 100.0,  # Non-zero cost price (no error)
            "partdesc": "Valid part",
            # Add required values for _read_outputs
            "profileentry": "HS110",
            "measureentry": "metric",
            "ODentry": 100.0,
            "IDentry": 80.0,
            "CHentry": 10.0,
            "extraentry": 25,
            "MATL1entry": "EPDM",
            "D6": "REF123",
            "D7": "metric",
            "D8": 100.0,
            "D9": 80.0,
            "D10": 10.0,
            "Type1": "Standard",
            "C31": "PART123",
            "C19": 150.0,
            "C20": 200.0,
            "C21": 250.0,
            "billetreqd1": "Yes",
            "mctime1": 30.0,
            "mmreqd1": "Material1",
        }
        self.mock_excel_service.read_cell.side_effect = lambda cell: mock_cell_values.get(cell, "")
        self.mock_excel_service.read_cells_batch.return_value = mock_cell_values

        inputs = WorkbookInputs(
            profile="HS110",
            measure="metric",
            od=float("100.0"),
            id=float("80.0"),
            ch=float("10.0"),
            material1="EPDM",
            extra=25,  # Provided - validation should pass
        )

        # Process should complete and run pricing calculation
        outputs, error = self.configurator_service.process(inputs)

        # Should return outputs (not None) and no error
        self.assertIsNotNone(outputs)
        self.assertIsNone(error)

        # Verify that execute_macro (pricing calculation) WAS called
        self.mock_excel_service.execute_macro.assert_called_once_with("runme")

    def test_automatic_pricing_calculation_when_validation_passes(self):
        """Test that pricing calculation runs automatically when all required inputs are provided."""
        # Setup data dictionary for consistent mocking
        mock_data = {
            "extratype": "Qty Vees",  # Requires extra
            "type2": "",
            "Type3": "",
            "costpriceevco": 100.0,  # Non-zero cost price (no error)
            "partdesc": "Valid part",
            # Add required values for _read_outputs
            "profileentry": "HS110",
            "measureentry": "metric",
            "ODentry": 100.0,
            "IDentry": 80.0,
            "CHentry": 10.0,
            "extraentry": 25,
            "MATL1entry": "EPDM",
            "D6": "REF123",
            "D7": "metric",
            "D8": 100.0,
            "D9": 80.0,
            "D10": 10.0,
            "Type1": "Standard",
            "C31": "PART123",
            "C19": 150.0,
            "C20": 200.0,
            "C21": 250.0,
            "billetreqd1": "Yes",
            "mctime1": 30.0,
            "mmreqd1": "Material1",
        }

        # Setup mocks for both read_cell and read_cells_batch
        self.mock_excel_service.read_cell.side_effect = lambda cell: mock_data.get(cell, "")
        self.mock_excel_service.read_cells_batch.return_value = mock_data

        inputs = WorkbookInputs(
            profile="HS110",
            measure="metric",
            od=float("100.0"),
            id=float("80.0"),
            ch=float("10.0"),
            material1="EPDM",
            extra=25,  # All required fields provided - validation should pass
        )

        # Process should automatically run pricing calculation since validation passes
        outputs, error = self.configurator_service.process(inputs)

        # Should return outputs (not None) and no error
        self.assertIsNotNone(outputs)
        self.assertIsNone(error)

        # Verify that execute_macro (pricing calculation) WAS called automatically
        self.mock_excel_service.execute_macro.assert_called_once_with("runme")

    def test_format_cached_float_preserves_precision(self):
        """Test that _format_cached_float rounds to 3 decimal places to match Excel display."""
        # Test with a value that has floating-point precision issues
        result = self.configurator_service._format_cached_float(21.653543307086615)

        # Should be rounded to 3 decimal places
        self.assertEqual(result, 21.654)

        # Test with None value
        result = self.configurator_service._format_cached_float(None)
        self.assertEqual(result, 0.0)

        # Test with string that can be converted to float
        result = self.configurator_service._format_cached_float("123.456789")
        self.assertEqual(result, 123.457)

        # Test with non-numeric string
        result = self.configurator_service._format_cached_float("refresh data")
        self.assertEqual(result, 0.0)

    def test_detect_error_handles_text_cost_price(self):
        """Test that _detect_error handles text cost prices like 'Contact Evco Seals' without error."""
        # Test with text cost price (should not trigger error detection)
        self.mock_excel_service.read_cell.return_value = "Contact Evco Seals"

        result = self.configurator_service._detect_error()

        # Should return None (no error detected) and not raise an exception
        self.assertIsNone(result)

    def test_detect_material_unavailable_with_text_only(self):
        """Test _detect_material_unavailable with only part description containing material unavailable."""
        mock_cell_values = {
            "partdesc": "Material/Size not available for this configuration",
            "costpriceevco": "15.50",  # Numeric cost price
        }
        self.mock_excel_service.read_cell.side_effect = lambda cell: mock_cell_values.get(cell, None)

        result = self.configurator_service._detect_material_unavailable()

        self.assertEqual(result, "Material/Size not available for this configuration")

    def test_detect_material_unavailable_with_cost_price_text(self):
        """Test _detect_material_unavailable with both part description and text cost price."""
        mock_cell_values = {
            "partdesc": "Material/Size not available",
            "costpriceevco": "Contact Evco Seals",  # Text cost price
        }
        self.mock_excel_service.read_cell.side_effect = lambda cell: mock_cell_values.get(cell, None)

        result = self.configurator_service._detect_material_unavailable()

        self.assertEqual(result, "Material/Size not available. Contact Evco Seals.")

    def test_detect_material_unavailable_no_match(self):
        """Test _detect_material_unavailable when no material unavailable text is found."""
        mock_cell_values = {
            "partdesc": "Standard seal configuration",
            "costpriceevco": "15.50",
        }
        self.mock_excel_service.read_cell.side_effect = lambda cell: mock_cell_values.get(cell, None)

        result = self.configurator_service._detect_material_unavailable()

        self.assertIsNone(result)

    def test_detect_material_unavailable_with_numeric_cost_price(self):
        """Test _detect_material_unavailable doesn't append numeric cost price."""
        mock_cell_values = {
            "partdesc": "Material/Size not available",
            "costpriceevco": "25.75",  # Numeric cost price (should not be appended)
        }
        self.mock_excel_service.read_cell.side_effect = lambda cell: mock_cell_values.get(cell, None)

        result = self.configurator_service._detect_material_unavailable()

        self.assertEqual(result, "Material/Size not available")

    def test_detect_error_catches_material_unavailable(self):
        """Test that _detect_error catches material unavailable as an error."""
        mock_cell_values = {
            "partdesc": "Material/Size not available for this profile",
            "costpriceevco": "Contact Evco Seals",
        }
        self.mock_excel_service.read_cell.side_effect = lambda cell: mock_cell_values.get(cell, None)

        result = self.configurator_service._detect_error()

        self.assertEqual(result, "Material/Size not available for this profile. Contact Evco Seals.")

    def test_process_returns_error_for_material_unavailable(self):
        """Test that process returns error when material is unavailable."""
        mock_cell_values = {
            "partdesc": "Material/Size not available",
            "costpriceevco": "Contact Evco Seals",
            # Add validation cells to pass validation
            "extratype": "Qty Vees",  # Matches test_inputs.extra=4
            "type2": "T/A Material",  # Matches test_inputs.material2="NB85"
            "Type3": "",  # Empty, so material3=None is OK
        }
        self.mock_excel_service.read_cell.side_effect = lambda cell: mock_cell_values.get(cell, None)
        self.mock_excel_service.read_cells_batch.return_value = mock_cell_values

        result, error = self.configurator_service.process(self.test_inputs)

        self.assertIsNone(result)
        self.assertEqual(error, "Material/Size not available. Contact Evco Seals.")
