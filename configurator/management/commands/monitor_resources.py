"""
Django management command to monitor system resources and cleanup when thresholds are exceeded.

This command checks current CPU and RAM usage. When either threshold is exceeded,
it automatically cleans up all Excel processes and session files to free up system resources.
Designed to be run periodically (e.g., every 5 minutes) via cron or scheduler.
"""

import logging

import djclick as click

from configurator.services import WorkbookSessionService
from configurator.services.resource_monitor import ResourceMonitor, ResourceMonitorError

logger = logging.getLogger(__name__)


@click.command()
def command():
    """Monitor system resources and cleanup when thresholds are exceeded."""

    try:
        resource_monitor = ResourceMonitor()
        session_service = WorkbookSessionService.get_service()

        _check_and_cleanup(resource_monitor, session_service)

    except ResourceMonitorError as e:
        click.echo(click.style(f"Resource monitor error: {e}", fg="red"))
        raise click.ClickException(str(e))
    except Exception as e:
        click.echo(click.style(f"Unexpected error: {e}", fg="red"))
        raise


def _check_and_cleanup(resource_monitor: ResourceMonitor, session_service: WorkbookSessionService):
    """Check resource usage and perform cleanup if thresholds are exceeded."""

    try:
        # Get current system info
        system_info = resource_monitor.get_system_info()

        ram_usage = system_info.get("ram_usage_percent", 0)
        cpu_usage = system_info.get("cpu_usage_percent", 0)
        ram_exceeded = system_info.get("ram_threshold_exceeded", False)
        cpu_exceeded = system_info.get("cpu_threshold_exceeded", False)

        # Check if cleanup is needed
        if ram_exceeded or cpu_exceeded:
            threshold_type = []
            if ram_exceeded:
                threshold_type.append(f"RAM ({ram_usage:.1f}%)")
            if cpu_exceeded:
                threshold_type.append(f"CPU ({cpu_usage:.1f}%)")

            threshold_msg = " and ".join(threshold_type)
            click.echo(f"⚠️  {threshold_msg} threshold exceeded - performing cleanup")

            # Perform cleanup
            cleanup_results = _perform_emergency_cleanup(session_service)

            # Report cleanup results
            sessions_cleaned = cleanup_results["sessions_cleaned"]
            excel_killed = cleanup_results["excel_killed"]

            click.echo(f"✅ Cleanup completed: {sessions_cleaned} sessions cleaned, {excel_killed} Excel processes killed")

            logger.info(
                f"Emergency cleanup triggered - {threshold_msg} exceeded. "
                f"Cleaned {sessions_cleaned} sessions, killed {excel_killed} Excel processes"
            )

    except Exception as e:
        logger.error(f"Error during resource check: {e}")
        click.echo(f"Error checking resources: {e}")


def _perform_emergency_cleanup(session_service: WorkbookSessionService) -> dict:
    """Perform emergency cleanup of sessions and Excel processes."""

    try:
        # Cleanup all sessions and session files
        sessions_cleaned = session_service.cleanup_all_sessions()

        # Cleanup Excel processes
        excel_killed = session_service.cleanup_excel_processes()

        return {
            "sessions_cleaned": sessions_cleaned,
            "excel_killed": excel_killed,
        }

    except Exception as e:
        logger.error(f"Error during emergency cleanup: {e}")
        return {
            "sessions_cleaned": 0,
            "excel_killed": 0,
            "error": str(e),
        }
