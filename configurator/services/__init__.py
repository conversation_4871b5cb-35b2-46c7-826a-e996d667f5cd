"""
Services module for the configurator app.

This module provides service classes for Excel automation, session management,
and other business logic operations.
"""

from .excel import (
    ExcelService,
    ExcelServiceError,
    MacroExecutionError,
    WorkbookNotFoundError,
)
from .resource_monitor import ResourceMonitor, ResourceMonitorError
from .workbook import WorkbookCells, WorkbookService, WorkbookServiceError
from .workbook_session import (
    WorkbookSessionError,
    WorkbookSessionService,
)

__all__ = [
    "ExcelService",
    "ExcelServiceError",
    "WorkbookNotFoundError",
    "MacroExecutionError",
    "ResourceMonitor",
    "ResourceMonitorError",
    "WorkbookService",
    "WorkbookServiceError",
    "WorkbookCells",
    "WorkbookSessionError",
    "WorkbookSessionService",
]
