"""
Simplified VBA Dialog Detection Service

This service provides VBA error dialog detection and dismissal for Excel automation.
Simplified to work with Django management commands without threading complexity.
"""

import logging
from typing import TYPE_CHECKING, List, NamedTuple, Optional

if TYPE_CHECKING:
    import pywinauto
else:
    try:
        import pywinauto
    except ImportError:
        pywinauto = None  # type: ignore

logger = logging.getLogger(__name__)


class DialogInfo(NamedTuple):
    """Information about a detected VBA dialog."""

    title: str
    error_message: Optional[str]


class VBADialogError(Exception):
    """Raised when a VBA dialog is detected with an error message."""


class VBADialogDetector:
    """
    Simplified VBA dialog detection and dismissal service.

    This class provides a single method to check for VBA dialogs,
    extract error messages, and optionally dismiss them.
    """

    def __init__(self, timeout: float = 2.0):
        """
        Initialize the VBA dialog detector.

        Args:
            timeout: Timeout for individual dialog detection operations (seconds)
            auto_dismiss: Whether to automatically dismiss detected dialogs
        """
        self.timeout = timeout

        # Dialog patterns to detect
        self.dialog_patterns = [
            "Microsoft Visual Basic",
            "Visual Basic",
            "Runtime Error",
            "Microsoft Excel",
            "Run-time error",
            "Compile Error",
            "Error",
        ]

        # Backends to try (in order of preference)
        self.backends = ["uia", "win32"]

    def check_for_dialogs(self) -> List[DialogInfo]:
        """
        Check for VBA dialogs and optionally dismiss them.

        Returns:
            List of DialogInfo objects for each detected dialog
        """
        if not pywinauto:
            logger.debug("pywinauto not available, skipping VBA dialog detection")
            return []

        detected_dialogs = []

        try:
            # Try desktop enumeration approach with multiple backends
            for backend in self.backends:
                dialogs_found = self._scan_desktop_windows(backend)
                detected_dialogs.extend(dialogs_found)

            # Try direct connection approach
            dialogs_found = self._scan_by_title_connection()
            detected_dialogs.extend(dialogs_found)

        except Exception as e:
            logger.debug(f"Error during VBA dialog detection: {e}")

        return detected_dialogs

    def _scan_desktop_windows(self, backend: str) -> List[DialogInfo]:
        """
        Scan all desktop windows for VBA dialogs using specified backend.

        Args:
            backend: pywinauto backend to use ("uia" or "win32")

        Returns:
            List of detected dialogs
        """
        detected_dialogs = []

        try:
            from pywinauto import Desktop

            desktop = Desktop(backend=backend)
            windows = desktop.windows()

            for window in windows:
                try:
                    title = window.window_text()
                    if not title:
                        continue

                    if self._should_skip_window(title):
                        continue

                    if self._is_vba_dialog(title, window):
                        logger.debug(f"Found potential VBA dialog: '{title}'")

                        error_message = self._extract_error_message(window)

                        self._dismiss_dialog(window)

                        detected_dialogs.append(DialogInfo(title, error_message))

                except Exception as e:
                    logger.debug(f"Error checking window: {e}")
                    continue

        except Exception as e:
            logger.debug(f"Error scanning desktop with {backend} backend: {e}")

        return detected_dialogs

    def _scan_by_title_connection(self) -> List[DialogInfo]:
        """
        Scan for VBA dialogs by trying to connect to windows with known titles.

        Returns:
            List of detected dialogs
        """
        detected_dialogs = []

        for title_pattern in self.dialog_patterns:
            try:
                app = pywinauto.Application()
                app.connect(title=title_pattern, timeout=0.5)

                dialog = app.window(title=title_pattern)

                if dialog.exists(timeout=0.5):
                    logger.debug(f"Found VBA dialog via connection: '{title_pattern}'")

                    error_message = self._extract_error_message(dialog)

                    self._dismiss_dialog(dialog)

                    detected_dialogs.append(DialogInfo(title_pattern, error_message))

            except Exception:
                continue

        return detected_dialogs

    def _should_skip_window(self, title: str) -> bool:
        """
        Check if a window should be skipped based on its title.

        Args:
            title: Window title to check

        Returns:
            True if the window should be skipped
        """
        title_lower = title.lower()

        # Skip browser windows and other non-Excel applications
        skip_patterns = ["edge", "chrome", "firefox", "browser", "pages - work", "microsoft​"]

        return any(pattern in title_lower for pattern in skip_patterns)

    def _is_vba_dialog(self, title: str, window) -> bool:
        """
        Check if a window is likely a VBA dialog.

        Args:
            title: Window title
            window: pywinauto window object

        Returns:
            True if this appears to be a VBA dialog
        """
        # Check title patterns
        title_match = any(pattern.lower() in title.lower() for pattern in self.dialog_patterns)

        if not title_match:
            return False

        # Additional validation - check window class
        try:
            class_name = window.class_name()
            excel_classes = ["#32770", "dialog", "excel"]
            if not any(excel_class in class_name.lower() for excel_class in excel_classes):
                return False
        except Exception:
            # If we can't get class name, assume it might be a dialog
            pass

        return True

    def _extract_error_message(self, dialog) -> Optional[str]:
        """
        Extract error message text from a VBA dialog.

        Args:
            dialog: pywinauto dialog window

        Returns:
            Error message text or None if not found
        """
        try:
            # Try to get all text from the dialog
            dialog_text = dialog.window_text()

            # Also try to get text from static text controls
            try:
                static_controls = dialog.children(class_name="Static")
                for control in static_controls:
                    control_text = control.window_text()
                    # Look for the actual error message - usually the longest text
                    if control_text and len(control_text) > len(dialog_text):
                        dialog_text = control_text
                    # Also check for runtime error patterns
                    if control_text and ("Run-time error" in control_text or "runtime error" in control_text.lower()):
                        dialog_text = control_text
            except Exception:
                pass

            # Clean up the error message
            if dialog_text:
                # Handle VBA error format: "Run-time error 'code': message"
                if "run-time error" in dialog_text.lower():
                    return dialog_text.strip()

                # Remove common prefixes and clean up
                error_text = dialog_text.replace("Microsoft Visual Basic", "").strip()
                if error_text.startswith("-"):
                    error_text = error_text[1:].strip()

                # If we have meaningful error text, return it
                if error_text and len(error_text) > 5:
                    return error_text

                # Otherwise return the original dialog text
                return dialog_text.strip()

            return None

        except Exception as e:
            logger.debug(f"Could not extract error message from dialog: {e}")
            return None

    def _dismiss_dialog(self, dialog) -> bool:
        """
        Dismiss a VBA error dialog by clicking the second "End" button

        Args:
            dialog: pywinauto dialog window

        Returns:
            True if dialog was successfully dismissed, False otherwise
        """
        try:
            buttons = dialog.children(class_name="Button")
            if len(buttons) >= 2:
                second_button = buttons[1]  # Second button from left (0-indexed)
                second_button.click()
                logger.debug("Clicked End buton to dismiss VBA dialog")
                return True

        except Exception as e:
            logger.debug(f"Error dismissing VBA dialog: {e}")
            return False
