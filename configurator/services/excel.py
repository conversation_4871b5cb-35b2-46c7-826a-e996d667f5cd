"""
Excel automation service for workbook operations.

This service provides a high-level interface for Excel automation using xlwings,
including workbook management, cell operations, and macro execution with proper
error handling and resource cleanup.
"""

import logging
import threading
import time
from pathlib import Path
from typing import Any, Dict, List, Optional

import xlwings as xw

from configurator.services.resource_monitor import ResourceMonitor

logger = logging.getLogger(__name__)

# Global lock to prevent concurrent Excel operations across all sessions
# This prevents race conditions when multiple requests try to use Excel simultaneously
_excel_operation_lock = threading.RLock()


class ExcelServiceError(Exception):
    """Base exception for Excel service errors."""


class WorkbookNotFoundError(ExcelServiceError):
    """Raised when a workbook file is not found."""


class MacroExecutionError(ExcelServiceError):
    """Raised when macro execution fails."""


class ExcelRPCError(ExcelServiceError):
    """Raised when Excel RPC/COM communication fails."""


# Common Excel COM error codes
COM_ERROR_RPC_UNAVAILABLE = -2147023174  # "The RPC server is unavailable"
COM_ERROR_RPC_FAILED = -2147023170  # "The remote procedure call failed"
COM_ERROR_EXCEPTION = -2147352567  # "Exception occurred"


class ExcelService:
    """
    Service for automating Excel workbook operations using xlwings.

    Provides methods for opening workbooks, reading/writing cells, executing macros,
    and proper resource cleanup.
    """

    def __init__(self, workbook_path: Path, visible: bool = False):
        if not workbook_path:
            raise ExcelServiceError("Workbook path is required")

        self.visible = visible
        self._validate_path(workbook_path)
        self.workbook_path = workbook_path
        self.workbook: Optional[xw.Book] = None

    @classmethod
    def open(cls, workbook_path: Path, visible: bool = False) -> "ExcelService":
        """
        Create and open an ExcelService instance.

        This method first tries to connect to an already open workbook with the same
        file path. If no such workbook is found, it opens a new instance.

        This approach provides better performance by reusing existing Excel instances
        while maintaining thread safety by creating separate ExcelService objects
        that reference the same underlying workbook.

        Args:
            workbook_path: Path to the Excel workbook
            visible: Whether Excel should be visible

        Returns:
            ExcelService instance connected to the workbook
        """
        service = cls(workbook_path, visible)
        service._connect_or_open()
        return service

    def _validate_path(self, path: Path):
        if not path.exists():
            raise WorkbookNotFoundError(f"Workbook file not found: {path}")
        if not path.is_file():
            raise WorkbookNotFoundError(f"Path is not a file: {path}")
        if path.suffix.lower() not in [".xlsx", ".xlsm", ".xls"]:
            raise WorkbookNotFoundError(f"Invalid Excel file format: {path}")

    def _is_rpc_error(self, error: Exception) -> bool:
        """
        Check if the error is an RPC/COM communication error.

        Args:
            error: Exception to check

        Returns:
            True if this is an RPC error that indicates Excel COM server failure
        """
        if hasattr(error, "args") and error.args:
            error_code = error.args[0] if isinstance(error.args[0], int) else None
            return error_code in [COM_ERROR_RPC_UNAVAILABLE, COM_ERROR_RPC_FAILED]
        return False

    def _handle_rpc_error(self, operation: str, error: Exception) -> None:
        """
        Handle RPC/COM errors by cleaning up Excel processes and raising appropriate exception.

        Args:
            operation: Description of the operation that failed
            error: The RPC error that occurred
        """
        logger.error(f"RPC error during {operation}: {error}")
        logger.info("Attempting to clean up Excel processes due to RPC failure...")

        try:
            # Force close this workbook connection
            self.workbook = None

            # Clean up all Excel processes to recover from COM server failure
            self.cleanup_excel_processes()

        except Exception as cleanup_error:
            logger.error(f"Error during Excel cleanup: {cleanup_error}")

        raise ExcelRPCError(f"Excel COM server failure during {operation}: {error}") from error

    def _open(self) -> None:
        """Open the Excel workbook."""
        app = xw.App(visible=self.visible, add_book=False, display_alerts=False)

        self.workbook = app.books.open(str(self.workbook_path), read_only=True)

        # Try to activate the Main sheet if it exists
        try:
            self.workbook.sheets["Main"].activate()
        except KeyError:
            # Main sheet doesn't exist, which is acceptable
            pass

    def _connect_or_open(self) -> None:
        """
        Connect to an existing open workbook or open it if not already open.

        This method implements a performance optimization by trying to connect
        to an already open workbook before creating a new Excel instance.
        """
        try:
            # First, try to connect to an existing workbook with the same path
            # xlwings.Book() searches across all Excel instances for the workbook
            self.workbook = xw.Book(str(self.workbook_path))
            logger.debug(f"Connected to existing workbook: {self.workbook_path}")

            # Log connection success
            logger.debug(f"Successfully connected to existing workbook: {self.workbook_path}")

        except FileNotFoundError:
            # Workbook is not currently open, so open it normally
            logger.debug(f"Workbook not open, opening new instance: {self.workbook_path}")
            self._open()
            return

        except Exception as e:
            # If connection fails for any other reason, fall back to opening normally
            logger.warning(f"Failed to connect to existing workbook, opening new instance: {e}")
            self._open()
            return

        # Try to activate the Main sheet if it exists
        try:
            self.workbook.sheets["Main"].activate()
        except KeyError:
            # Main sheet doesn't exist, which is acceptable
            pass
        except Exception as e:
            # If activation fails, log but continue (workbook is still usable)
            logger.debug(f"Could not activate Main sheet: {e}")

    def _ensure_workbook_active(self) -> None:
        """
        Ensure this workbook is the active workbook in Excel.

        This is critical when multiple workbooks are open in the same Excel instance
        to prevent cross-session contamination where operations might affect the
        wrong workbook.
        """
        if not self.workbook:
            return

        try:
            # Activate our specific workbook
            self.workbook.activate()

            # Also ensure the Main sheet is active if it exists
            try:
                self.workbook.sheets["Main"].activate()
            except KeyError:
                # Main sheet doesn't exist, use the first sheet
                if self.workbook.sheets:
                    self.workbook.sheets[0].activate()

        except Exception as e:
            # Log warning but don't fail - the workbook might still be usable
            logger.warning(f"Could not activate workbook {self.workbook_path}: {e}")

    def close(self) -> None:
        """
        Close the workbook connection.

        Note: This method only disconnects from the workbook without closing
        the actual Excel file, allowing other ExcelService instances to continue
        using the same workbook. This is part of the performance optimization
        strategy.
        """
        if not self.workbook:
            return

        try:
            # Only disconnect from the workbook, don't close it
            # This allows other ExcelService instances to continue using it
            logger.debug(f"Disconnecting from workbook: {self.workbook_path}")
            self.workbook = None
        except Exception as e:
            logger.error("Failed to disconnect from workbook: %s", e)
            self.workbook = None

    def force_close(self) -> None:
        """
        Force close the workbook and quit Excel application.

        This method actually closes the Excel workbook and quits the application.
        Use this only when you're certain no other processes need the workbook.
        """
        if not self.workbook:
            return

        try:
            app = self.workbook.app
            workbook_path = self.workbook_path
            self.workbook.close()
            app.quit()
            logger.debug(f"Force closed workbook and Excel app: {workbook_path}")
        except Exception as e:
            logger.error("Failed to force close workbook: %s", e)
        finally:
            self.workbook = None

    def write_cell(self, cell_ref: str, value: Any) -> None:
        """
        Write a value to a named cell or cell reference.

        Args:
            cell_ref: Named range or cell reference (e.g., "A1", "profileentry")
            value: Value to write to the cell

        Raises:
            ExcelServiceError: If no workbook is open
            ExcelRPCError: If Excel COM server fails
            CellReferenceError: If cell reference is invalid
        """
        if not self.workbook:
            raise ExcelServiceError("No workbook is currently open")

        # Use global lock to prevent concurrent Excel operations
        with _excel_operation_lock:
            # Ensure our workbook is active to prevent cross-session contamination
            self._ensure_workbook_active()

            try:
                # Try named range first
                self.workbook.names[cell_ref].refers_to_range.value = value
            except Exception as e:
                # Check for RPC errors first
                if self._is_rpc_error(e):
                    self._handle_rpc_error(f"writing cell {cell_ref}", e)

                # Fall back to cell reference
                try:
                    # Use our specific workbook's active sheet, not Excel's global active sheet
                    cell = self.workbook.sheets.active.range(cell_ref)
                    cell.value = value
                except Exception as e2:
                    # Check for RPC errors in fallback attempt
                    if self._is_rpc_error(e2):
                        self._handle_rpc_error(f"writing cell {cell_ref} (fallback)", e2)

                    logger.error("Failed to write cell %s: %s", cell_ref, e2)
                    raise ExcelServiceError(str(e2)) from e2

    def read_cell(self, cell_ref: str) -> Any:
        """
        Read a value from a named cell or cell reference.

        Args:
            cell_ref: Named range or cell reference (e.g., "A1", "profileentry")

        Returns:
            Value from the cell

        Raises:
            ExcelServiceError: If no workbook is open
            ExcelRPCError: If Excel COM server fails
            CellReferenceError: If cell reference is invalid
        """
        if not self.workbook:
            raise ExcelServiceError("No workbook is currently open")

        # Use global lock to prevent concurrent Excel operations
        with _excel_operation_lock:
            # Ensure our workbook is active to prevent cross-session contamination
            self._ensure_workbook_active()

            try:
                # Try named range first
                return self.workbook.names[cell_ref].refers_to_range.value
            except Exception as e:
                # Check for RPC errors first
                if self._is_rpc_error(e):
                    self._handle_rpc_error(f"reading cell {cell_ref}", e)

                # Fall back to cell reference
                try:
                    # Use our specific workbook's active sheet, not Excel's global active sheet
                    return self.workbook.sheets.active.range(cell_ref).value
                except Exception as e2:
                    # Check for RPC errors in fallback attempt
                    if self._is_rpc_error(e2):
                        self._handle_rpc_error(f"reading cell {cell_ref} (fallback)", e2)

                    logger.error("Failed to read cell %s: %s", cell_ref, e2)
                    raise ExcelServiceError(str(e2)) from e2

    def read_cells_batch(self, cell_refs: List[str]) -> Dict[str, Any]:
        """
        Read multiple cells in a single batch operation for improved performance.

        Args:
            cell_refs: List of named ranges or cell references to read

        Returns:
            Dictionary mapping cell references to their values

        Raises:
            ExcelServiceError: If no workbook is open
        """
        if not self.workbook:
            raise ExcelServiceError("No workbook is currently open")

        if not cell_refs:
            return {}

        # Use global lock to prevent concurrent Excel operations
        with _excel_operation_lock:
            # Ensure our workbook is active to prevent cross-session contamination
            self._ensure_workbook_active()

            results = {}
            for cell_ref in cell_refs:
                try:
                    # Try named range first
                    value = self.workbook.names[cell_ref].refers_to_range.value
                    results[cell_ref] = value
                except Exception:
                    # Fall back to cell reference
                    try:
                        # Use our specific workbook's active sheet, not Excel's global active sheet
                        value = self.workbook.sheets.active.range(cell_ref).value
                        results[cell_ref] = value
                    except Exception as e:
                        logger.error("Failed to read cell %s: %s", cell_ref, e)
                        # Store None for failed reads rather than failing the entire batch
                        results[cell_ref] = None

            return results

    def execute_macro(self, macro_name: str) -> None:
        """
        Execute a VBA macro in the workbook.

        Args:
            macro_name: Name of the macro to execute

        Raises:
            ExcelServiceError: If no workbook is open
            MacroExecutionError: If macro execution fails

        """
        if not self.workbook:
            raise ExcelServiceError("No workbook is currently open")

        # Use global lock to prevent concurrent Excel operations
        with _excel_operation_lock:
            # Ensure our workbook is active to prevent cross-session contamination
            self._ensure_workbook_active()

            original_calculation = None

            try:
                # Ensure calculation is set to automatic before running macro
                original_calculation = self.workbook.app.calculation
                self.workbook.app.calculation = "automatic"

                # Force a calculation update before running macro
                self.workbook.app.calculate()

                # Add a small delay to ensure Excel is ready after batch operations
                time.sleep(0.1)

                # Execute the macro - display_alerts=False should prevent modal dialogs
                logger.debug(f"Executing macro '{macro_name}' on workbook: {self.workbook.name}")
                self.workbook.macro(macro_name)()
                logger.debug(f"Successfully executed macro '{macro_name}'")

            except Exception as e:
                logger.error(f"Macro execution failed for '{macro_name}': {str(e)}")
                logger.error(f"Workbook name: {self.workbook.name if self.workbook else 'None'}")
                logger.error(f"Workbook path: {self.workbook.fullname if self.workbook else 'None'}")

                # Check for RPC errors and handle them specially
                if self._is_rpc_error(e):
                    self._handle_rpc_error(f"executing macro '{macro_name}'", e)

                raise MacroExecutionError(f"Failed to execute macro '{macro_name}': {str(e)}")
            finally:
                # Restore original settings
                if self.workbook and self.workbook.app:
                    if original_calculation is not None:
                        self.workbook.app.calculation = original_calculation

    def cleanup_excel_processes(self) -> None:
        """Clean up any orphaned Excel processes."""
        try:
            resource_monitor = ResourceMonitor()
            excel_processes = resource_monitor.get_excel_processes()

            for proc in excel_processes:
                proc.terminate()
        except Exception:
            pass
