{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Django HTTP",
      "type": "debugpy",
      "request": "launch",
      "module": "django",
      "args": ["runserver", "0.0.0.0:9090"],
      "console": "integratedTerminal",
      "django": true,
      "justMyCode": false,
      "env": {
        "PYTHONPATH": "${workspaceFolder}",
        "DOPPLER_ENV": "1"
      }
    },
    {
      "name": "HTTP Plus Server",
      "type": "debugpy",
      "request": "launch",
      "program": "manage.py",
      "args": ["tailwind", "runserver", "[::]:9090"],
      "django": true,
      "justMyCode": false,
      "env": {
        "DOPPLER_ENV": "1"
      }
    },
    {
      "name": "manage.py",
      "type": "debugpy",
      "request": "launch",
      "program": "manage.py",
      "args": ["${command:pickArgs}"],
      "console": "integratedTerminal",
      "django": true,
      "justMyCode": false,
      "env": {
        "DOPPLER_ENV": "1"
      }
    },
  ]
}
